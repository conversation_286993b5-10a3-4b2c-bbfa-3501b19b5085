"use client"

import Image from "next/image"
import Link from "next/link"
import Header from "@/components/Header"
import Footer from "@/components/Footer"

export default function SampleBlogPost() {
  return (
    <div className="min-h-screen bg-white">
      {/* Fixed Header */}
      <div className="fixed top-0 left-0 w-full z-30">
        <Header transparent={false} />
      </div>
      
      {/* Spacer for Navbar height */}
      <div className="h-16 sm:h-20 lg:h-[80px]" />
      
      <main className="container mx-auto px-4 sm:px-6 lg:px-8 py-8 sm:py-12 md:py-16">
        {/* Back to Blog */}
        <Link 
          href="/blog" 
          className="inline-flex items-center text-blue-600 hover:text-blue-800 mb-6 sm:mb-8 transition-colors"
        >
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
          Back to Blog
        </Link>

        {/* Article Header */}
        <article className="max-w-4xl mx-auto">
          <header className="mb-8 sm:mb-12">
            <div className="mb-4">
              <span className="inline-block bg-blue-100 text-blue-800 text-xs sm:text-sm font-medium px-3 py-1 rounded-full">
                Product Innovation
              </span>
            </div>
            
            <h1 className="text-3xl sm:text-4xl md:text-5xl font-bold text-gray-900 mb-4 sm:mb-6 leading-tight">
              The Future of Digital Product Innovation: 
              <span className="block text-blue-600">Trends Shaping 2024</span>
            </h1>
            
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between text-gray-600 text-sm sm:text-base">
              <div className="flex items-center mb-2 sm:mb-0">
                <Image
                  src="/placeholder-user.jpg"
                  alt="Author"
                  width={40}
                  height={40}
                  className="rounded-full mr-3"
                />
                <div>
                  <p className="font-medium text-gray-900">Sarah Johnson</p>
                  <p className="text-sm text-gray-500">Lead Product Designer</p>
                </div>
              </div>
              <div className="text-sm text-gray-500">
                <time dateTime="2024-01-15">January 15, 2024</time>
                <span className="mx-2">•</span>
                <span>8 min read</span>
              </div>
            </div>
          </header>

          {/* Featured Image */}
          <div 
            className="relative w-full h-64 sm:h-80 md:h-96 mb-8 sm:mb-12 rounded-2xl overflow-hidden"
            style={{
              transform: 'perspective(1000px) rotateX(2deg)',
              boxShadow: '0 20px 40px rgba(0,0,0,0.1), 0 8px 16px rgba(0,0,0,0.06)'
            }}
          >
            <Image
              src="/placeholder.jpg"
              alt="Digital Innovation Trends"
              fill
              className="object-cover"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
          </div>

          {/* Article Content */}
          <div className="prose prose-lg max-w-none">
            <p className="text-xl text-gray-600 mb-8 leading-relaxed">
              As we navigate through 2024, the landscape of digital product innovation continues to evolve at an unprecedented pace. 
              From AI-driven experiences to sustainable design practices, here's what's shaping the future of product development.
            </p>

            <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-6 mt-12">
              1. AI-First Product Design
            </h2>
            
            <p className="text-gray-700 mb-6 leading-relaxed">
              Artificial Intelligence is no longer just a feature—it's becoming the foundation of how we design and build products. 
              Companies are shifting from AI-enhanced to AI-first approaches, where machine learning capabilities are integrated 
              from the ground up rather than bolted on as an afterthought.
            </p>

            <div 
              className="bg-blue-50 border-l-4 border-blue-500 p-6 my-8 rounded-r-lg"
              style={{
                transform: 'perspective(800px) rotateY(-1deg)',
                boxShadow: '0 4px 12px rgba(59, 130, 246, 0.1)'
              }}
            >
              <p className="text-blue-800 font-medium mb-2">💡 Key Insight</p>
              <p className="text-blue-700">
                "The most successful products of 2024 will be those that seamlessly blend human intuition with AI capabilities, 
                creating experiences that feel both intelligent and deeply personal."
              </p>
            </div>

            <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-6 mt-12">
              2. Sustainable UX Design
            </h2>
            
            <p className="text-gray-700 mb-6 leading-relaxed">
              Environmental consciousness is driving a new wave of sustainable UX design. This goes beyond just reducing 
              digital carbon footprints—it's about creating products that promote sustainable behaviors and mindful consumption.
            </p>

            <ul className="list-disc pl-6 mb-8 text-gray-700 space-y-2">
              <li>Dark mode as default to reduce energy consumption</li>
              <li>Minimalist interfaces that load faster and use less data</li>
              <li>Features that encourage users to make environmentally conscious choices</li>
              <li>Transparent reporting of digital environmental impact</li>
            </ul>

            <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-6 mt-12">
              3. Hyper-Personalization at Scale
            </h2>
            
            <p className="text-gray-700 mb-6 leading-relaxed">
              The era of one-size-fits-all digital experiences is ending. Advanced analytics and machine learning are enabling 
              hyper-personalization that adapts not just to user preferences, but to context, mood, and real-time behavior patterns.
            </p>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 my-8">
              <div 
                className="bg-gray-50 p-6 rounded-xl"
                style={{
                  transform: 'perspective(600px) rotateY(2deg)',
                  boxShadow: '0 8px 20px rgba(0,0,0,0.08)'
                }}
              >
                <h3 className="font-bold text-gray-900 mb-3">Before</h3>
                <p className="text-gray-600 text-sm">
                  Static interfaces with basic user preferences and limited customization options.
                </p>
              </div>
              <div 
                className="bg-blue-50 p-6 rounded-xl"
                style={{
                  transform: 'perspective(600px) rotateY(-2deg)',
                  boxShadow: '0 8px 20px rgba(59, 130, 246, 0.15)'
                }}
              >
                <h3 className="font-bold text-blue-900 mb-3">Now</h3>
                <p className="text-blue-700 text-sm">
                  Dynamic interfaces that adapt in real-time based on user behavior, context, and predictive analytics.
                </p>
              </div>
            </div>

            <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-6 mt-12">
              Looking Ahead
            </h2>
            
            <p className="text-gray-700 mb-6 leading-relaxed">
              The future of digital product innovation lies in the intersection of technology and human-centered design. 
              As we continue to push the boundaries of what's possible, the most successful products will be those that 
              enhance human capabilities while respecting our values and the planet we share.
            </p>

            <p className="text-gray-700 mb-8 leading-relaxed">
              At NYX, we're committed to staying at the forefront of these trends, helping our clients build products 
              that not only meet today's needs but anticipate tomorrow's opportunities.
            </p>

            {/* Call to Action */}
            <div 
              className="bg-gradient-to-r from-gray-900 to-blue-900 text-white p-8 rounded-2xl mt-12"
              style={{
                transform: 'perspective(1000px) rotateX(-1deg)',
                boxShadow: '0 20px 40px rgba(0,0,0,0.2)'
              }}
            >
              <h3 className="text-xl sm:text-2xl font-bold mb-4">Ready to innovate?</h3>
              <p className="mb-6 text-gray-200">
                Let's discuss how these trends can shape your next product. Our team is ready to help you build 
                the future of digital experiences.
              </p>
              <Link 
                href="/#contact-section"
                className="inline-block bg-white text-gray-900 px-6 py-3 rounded-lg font-medium hover:bg-gray-100 transition-colors"
              >
                Start Your Project
              </Link>
            </div>
          </div>
        </article>
      </main>

      <Footer />
    </div>
  )
}
