import { useState, useEffect } from 'react';

interface SectionConfig {
  ref: React.RefObject<HTMLElement>;
  isLightBackground: boolean;
}

export function useHeaderTransparency(sections: SectionConfig[]) {
  const [isTransparent, setIsTransparent] = useState(false);

  useEffect(() => {
    const handleHeaderState = () => {
      for (const section of sections) {
        if (!section.ref.current) continue;
        
        const rect = section.ref.current.getBoundingClientRect();
        
        if (rect.top <= 100 && rect.bottom > 100) {
          setIsTransparent(section.isLightBackground);
          return;
        }
      }
      
      setIsTransparent(false);
    };

    const onScroll = () => {
      handleHeaderState();
    };
    
    window.addEventListener('scroll', onScroll);
    // Initial check
    handleHeaderState();

    return () => {
      window.removeEventListener('scroll', onScroll);
    };
  }, [sections]);

  return isTransparent;
}
