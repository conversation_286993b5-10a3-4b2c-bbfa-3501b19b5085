"use client"

import { Menu } from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { useState, useRef, useEffect } from "react"
import { useIsMobile } from "@/hooks/use-mobile"
import { useRouter, usePathname } from "next/navigation"
import { useScrollToSection } from "@/components/useScrollToSection"

const navLinks = [
  { name: "Home", href: "/" },
  { name: "About", href: "/about" },
  { name: "Blog", href: "/blog" },
  { name: "Contact", href: "#contact-section" },
  // Add more links here if needed
]

interface HeaderProps {
  transparent?: boolean;
}

function Header({ transparent = false }: HeaderProps) {
  const [open, setOpen] = useState(false)
  const isMobile = useIsMobile()
  // Add hover state
  const [hovered, setHovered] = useState(false)
  // Animation delay for each sphere
  const sphereDelays = [0, 100, 200, 300]
  // Add refs for menu and button
  const menuRef = useRef<HTMLDivElement>(null)
  const buttonRef = useRef<HTMLButtonElement>(null)
  const router = useRouter();
  const pathname = usePathname();
  const scrollToSection = useScrollToSection();

  useEffect(() => {
    if (!(open || hovered)) return;

    function handleClickOutside(event: MouseEvent | TouchEvent) {
      if (
        menuRef.current &&
        !menuRef.current.contains(event.target as Node) &&
        buttonRef.current &&
        !buttonRef.current.contains(event.target as Node)
      ) {
        setOpen(false);
        setHovered(false);
      }
    }

    function handleScroll() {
      setOpen(false);
      setHovered(false);
    }

    function handleResize() {
      setOpen(false);
      setHovered(false);
    }

    // Handle both mouse and touch events
    document.addEventListener('mousedown', handleClickOutside)
    document.addEventListener('touchstart', handleClickOutside)
    window.addEventListener('scroll', handleScroll)
    window.addEventListener('resize', handleResize)

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
      document.removeEventListener('touchstart', handleClickOutside)
      window.removeEventListener('scroll', handleScroll)
      window.removeEventListener('resize', handleResize)
    }
  }, [open, hovered])

  return (
    <header
      className={`text-white sticky top-0 z-50 transition-colors duration-300 h-16 sm:h-20 lg:h-[80px] ${transparent ? 'bg-transparent' : 'bg-gray-900'}`}
      style={transparent ? { background: 'transparent' } : { background: 'linear-gradient(90deg, #000 10%, #111827 50%, #000 100%)' }}
    >
      <style>{`
        .sphere-menu {
          display: flex;
          flex-direction: column;
          align-items: flex-end;
          position: absolute;
          right: -20px;
          top: 100%;
          margin-top: 0.5rem;
          z-index: 100;
          min-width: 120px;
          max-width: 90vw;
        }
        .sphere {
          width: 120px;
          height: 48px;
          margin: 0.25rem 0;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-weight: 600;
          font-size: 1rem;
          box-shadow: 0 6px 18px 0 rgba(30, 41, 59, 0.35), 0 1.5px 0 0 rgba(255,255,255,0.10) inset;
          cursor: pointer;
          opacity: 0;
          transform: translateY(-20px) scale(0.8);
          animation: sphereDrop 0.4s forwards;
          transition: transform 0.2s, box-shadow 0.2s;
          text-decoration: none;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .sphere-0 {
          background: linear-gradient(135deg, #0099ff 60%, #33ccff 100%);
        }
        .sphere-1 {
          background: linear-gradient(135deg, #e0ffff 60%, #ffffff 100%);
          color: #222;
        }
        .sphere-2 {
          background: linear-gradient(135deg, #888888 60%, #bbbbbb 100%);
        }
        .sphere-3 {
          background: linear-gradient(135deg, #222222 60%, #444444 100%);
        }
        .sphere-0:hover {
          background: linear-gradient(135deg, #0077b6 60%, #0099cc 100%);
        }
        .sphere-1:hover {
          background: linear-gradient(135deg, #b2d8d8 60%, #cccccc 100%);
          color: #111;
        }
        .sphere-2:hover {
          background: linear-gradient(135deg, #555555 60%, #888888 100%);
        }
        .sphere-3:hover {
          background: linear-gradient(135deg, #111111 60%, #222222 100%);
        }
        .sphere:hover {
          transform: translateY(0) scale(1.12);
          box-shadow: 0 12px 32px 0 rgba(30, 41, 59, 0.45), 0 2.5px 0 0 rgba(255,255,255,0.18) inset, 0 -2px 8px 0 rgba(0,0,0,0.10) inset;
        }
        .sphere-menu .sphere {
          pointer-events: auto;
        }
        @keyframes sphereDrop {
          to {
            opacity: 1;
            transform: translateY(0) scale(1);
          }
        }

        /* Touch-friendly improvements */
        .sphere {
          -webkit-tap-highlight-color: transparent;
          user-select: none;
          -webkit-user-select: none;
          -moz-user-select: none;
          -ms-user-select: none;
        }

        /* Prevent text selection on mobile */
        .sphere-menu {
          -webkit-touch-callout: none;
          -webkit-user-select: none;
          -khtml-user-select: none;
          -moz-user-select: none;
          -ms-user-select: none;
          user-select: none;
        }

        /* Better mobile scrolling if needed */
        @media (max-height: 600px) {
          .sphere-menu {
            max-height: 80vh;
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
          }
        }
        /* Tablet and small desktop */
        @media (max-width: 1024px) and (min-width: 641px) {
          .sphere {
            width: 110px !important;
            height: 44px !important;
            font-size: 0.9rem !important;
          }
          .sphere-menu {
            right: -15px !important;
          }
        }

        /* Mobile landscape and small tablets */
        @media (max-width: 640px) {
          .sphere {
            width: 100px !important;
            height: 40px !important;
            font-size: 0.875rem !important;
            border-radius: 8px !important;
          }
          .sphere-menu {
            right: -10px !important;
            max-width: 85vw !important;
          }
        }

        /* Mobile portrait */
        @media (max-width: 480px) {
          .sphere {
            width: 90px !important;
            height: 36px !important;
            font-size: 0.8rem !important;
            border-radius: 6px !important;
          }
          .sphere-menu {
            right: 0px !important;
            max-width: 80vw !important;
          }
        }

        /* Small mobile devices */
        @media (max-width: 375px) {
          .sphere {
            width: 85px !important;
            height: 34px !important;
            font-size: 0.75rem !important;
            padding: 0 8px !important;
          }
          .sphere-menu {
            right: 5px !important;
            max-width: 75vw !important;
          }
        }

        /* Very small mobile devices */
        @media (max-width: 320px) {
          .sphere {
            width: 80px !important;
            height: 32px !important;
            font-size: 0.7rem !important;
            padding: 0 6px !important;
          }
          .sphere-menu {
            right: 8px !important;
            max-width: 70vw !important;
          }
        }
      `}</style>
      <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 h-full flex justify-between items-center">
        {/* Logo Placeholder */}
        <Link href="/" className="flex items-center h-full">
          <div className="flex items-center">
            <Image
              src="/up (8).png"
              alt="Logo"
              width={40}
              height={40}
              className="h-8 sm:h-10 md:h-12 lg:h-14 w-auto object-contain"
            />
          </div>
        </Link>

        {/* Navigation */}
        <div className="flex items-center gap-2 sm:gap-4">
          {/* Desktop Navigation Links */}
          <nav className="hidden lg:flex items-center space-x-6">
            {navLinks.map((link) => (
              link.name === "Contact" ? (
                <button
                  key={link.name}
                  onClick={() => {
                    if (pathname === "/") {
                      scrollToSection("contact-section");
                    } else {
                      router.push('/#contact-section');
                    }
                  }}
                  className="text-white hover:text-gray-300 transition-colors duration-200 text-sm font-medium"
                >
                  {link.name}
                </button>
              ) : (
                <Link
                  key={link.name}
                  href={link.href}
                  className="text-white hover:text-gray-300 transition-colors duration-200 text-sm font-medium"
                >
                  {link.name}
                </Link>
              )
            ))}
          </nav>

          {/* Mobile Menu */}
          <div className="relative lg:hidden">
            <div className="flex items-center gap-1 sm:gap-2">
              <span className="text-xs sm:text-sm font-medium px-1 sm:px-2 hidden sm:block">Menu</span>
              <button
                ref={buttonRef}
                className="p-2 sm:p-2.5 rounded hover:bg-gray-800 focus:outline-none transition-colors touch-manipulation"
                onClick={() => setOpen((v) => !v)}
                onMouseEnter={() => setHovered(true)}
                onMouseLeave={() => setHovered(false)}
                onTouchStart={() => setHovered(true)}
                aria-label="Open navigation menu"
                style={{ minWidth: '44px', minHeight: '44px' }} // Touch target size
              >
                <Menu className="w-5 h-5 sm:w-6 sm:h-6" />
              </button>
            </div>

            {(open || hovered) && (
              <div
                ref={menuRef}
                className="sphere-menu"
                onMouseEnter={() => setHovered(true)}
                onMouseLeave={() => setHovered(false)}
                onTouchStart={() => setHovered(true)}
              >
                {navLinks.map((link, i) => (
                  link.name === "Contact" ? (
                    <a
                      key={link.name}
                      href="#contact-section"
                      aria-label={link.name}
                      style={{
                        animationDelay: `${sphereDelays[i]}ms`,
                        minHeight: '44px', // Touch target size
                        touchAction: 'manipulation'
                      }}
                      className={`sphere sphere-${i} flex items-center justify-center text-center touch-manipulation`}
                      onClick={e => {
                        setOpen(false);
                        setHovered(false);
                        if (pathname === "/") {
                          e.preventDefault();
                          scrollToSection("contact-section");
                        } else {
                          e.preventDefault();
                          router.push('/#contact-section');
                        }
                      }}
                      onTouchEnd={(e) => {
                        // Prevent double-tap zoom on mobile
                        e.preventDefault();
                      }}
                    >
                      {link.name}
                    </a>
                  ) : (
                    <Link
                      key={link.name}
                      href={link.href}
                      style={{
                        animationDelay: `${sphereDelays[i]}ms`,
                        minHeight: '44px', // Touch target size
                        touchAction: 'manipulation'
                      }}
                      className={`sphere sphere-${i} flex items-center justify-center text-center touch-manipulation`}
                      onClick={() => {
                        setOpen(false);
                        setHovered(false);
                      }}
                      onTouchEnd={(e) => {
                        // Prevent double-tap zoom on mobile
                        e.preventDefault();
                      }}
                    >
                      {link.name}
                    </Link>
                  )
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  )
}

export default Header;
