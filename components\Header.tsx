"use client"

import { Menu } from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { useState, useRef, useEffect } from "react"
import { useIsMobile } from "@/hooks/use-mobile"
import { useRouter, usePathname } from "next/navigation"
import { useScrollToSection } from "@/components/useScrollToSection"

const navLinks = [
  { name: "Home", href: "/" },
  { name: "About", href: "/about" },
  { name: "Blog", href: "/blog" },
  { name: "Contact", href: "#contact-section" },
  // Add more links here if needed
]

interface HeaderProps {
  transparent?: boolean;
}

function Header({ transparent = false }: HeaderProps) {
  const [open, setOpen] = useState(false)
  const isMobile = useIsMobile()
  // Add hover state
  const [hovered, setHovered] = useState(false)
  // Animation delay for each sphere
  const sphereDelays = [0, 100, 200, 300]
  // Add refs for menu and button
  const menuRef = useRef<HTMLDivElement>(null)
  const buttonRef = useRef<HTMLButtonElement>(null)
  const router = useRouter();
  const pathname = usePathname();
  const scrollToSection = useScrollToSection();

  useEffect(() => {
    if (!(open || hovered)) return;
    function handleClickOutside(event: MouseEvent) {
      if (
        menuRef.current &&
        !menuRef.current.contains(event.target as Node) &&
        buttonRef.current &&
        !buttonRef.current.contains(event.target as Node)
      ) {
        setOpen(false);
        setHovered(false);
      }
    }
    function handleScroll() {
      setOpen(false);
      setHovered(false);
    }
    document.addEventListener('mousedown', handleClickOutside)
    window.addEventListener('scroll', handleScroll)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
      window.removeEventListener('scroll', handleScroll)
    }
  }, [open, hovered])

  return (
    <header
      className={`text-white sticky top-0 z-50 transition-colors duration-300 ${transparent ? 'bg-transparent' : 'bg-gray-900'}`}
      style={transparent ? { background: 'transparent' } : { background: 'linear-gradient(90deg, #000 10%, #111827 50%, #000 100%)' }}
    >
      <style>{`
        .sphere-menu {
          display: flex;
          flex-direction: column;
          align-items: flex-end;
          position: absolute;
          right: -40px;
          margin-top: 0.5rem;
          z-index: 100;
        }
        .sphere {
          width: 120px;
          height: 48px;
          margin: 0.25rem 0;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-weight: 600;
          font-size: 1rem;
          box-shadow: 0 6px 18px 0 rgba(30, 41, 59, 0.35), 0 1.5px 0 0 rgba(255,255,255,0.10) inset;
          cursor: pointer;
          opacity: 0;
          transform: translateY(-20px) scale(0.8);
          animation: sphereDrop 0.4s forwards;
          transition: transform 0.2s, box-shadow 0.2s;
        }
        .sphere-0 {
          background: linear-gradient(135deg, #0099ff 60%, #33ccff 100%);
        }
        .sphere-1 {
          background: linear-gradient(135deg, #e0ffff 60%, #ffffff 100%);
          color: #222;
        }
        .sphere-2 {
          background: linear-gradient(135deg, #888888 60%, #bbbbbb 100%);
        }
        .sphere-3 {
          background: linear-gradient(135deg, #222222 60%, #444444 100%);
        }
        .sphere-0:hover {
          background: linear-gradient(135deg, #0077b6 60%, #0099cc 100%);
        }
        .sphere-1:hover {
          background: linear-gradient(135deg, #b2d8d8 60%, #cccccc 100%);
          color: #111;
        }
        .sphere-2:hover {
          background: linear-gradient(135deg, #555555 60%, #888888 100%);
        }
        .sphere-3:hover {
          background: linear-gradient(135deg, #111111 60%, #222222 100%);
        }
        .sphere:hover {
          transform: translateY(0) scale(1.12);
          box-shadow: 0 12px 32px 0 rgba(30, 41, 59, 0.45), 0 2.5px 0 0 rgba(255,255,255,0.18) inset, 0 -2px 8px 0 rgba(0,0,0,0.10) inset;
        }
        .sphere-menu .sphere {
          pointer-events: auto;
        }
        @keyframes sphereDrop {
          to {
            opacity: 1;
            transform: translateY(0) scale(1);
          }
        }
        @media (max-width: 400px) {
          .sphere {
            width: 90px !important;
            height: 36px !important;
            font-size: 0.95rem;
            border-radius: 8px !important;
          }
          .sphere-menu {
            right: 0px !important;
          }
          .relative > .flex > button {
            margin-right: 0.5rem;
          }
        }
      `}</style>
      <div className="container mx-4 px-2 sm:mx-[4rem] sm:px-4 sm:pr-4 py-0 flex justify-between items-center">
        {/* Logo Placeholder */}
        <Link href="/" className="flex items-center h-full">
          <div className="h-full flex items-center mt-2">
            <Image
              src="/up (8).png"
              alt="Logo"
              width={40}
              height={40}
              className="h-[5rem] w-auto object-contain py-2 mt-2 my-2"
            />
          </div>
        </Link>

        {/* Burger Menu */}
        <div className="relative">
          <div className="flex items-center gap-2">
            {!isMobile && (
              <span className="text-base font-medium px-2 hidden sm:block">All categories  <span className="text-base font-medium px-4">|</span></span>
            )}
            <button
              ref={buttonRef}
              className="p-2 rounded hover:bg-gray-800 focus:outline-none"
              onClick={() => setOpen((v) => !v)}
              onMouseEnter={() => setHovered(true)}
              onMouseLeave={() => setHovered(false)}
              aria-label="Open navigation menu"
            >
              <Menu className="w-7 h-7" />
            </button>
          </div>

          {(open || hovered) && (
            <div
              ref={menuRef}
              className="sphere-menu"
              onMouseEnter={() => setHovered(true)}
              onMouseLeave={() => setHovered(false)}
            >
              {navLinks.map((link, i) => (
                link.name === "Contact" ? (
                  <a
                    key={link.name}
                    href="#contact-section"
                    aria-label={link.name}
                    style={{
                      animationDelay: `${sphereDelays[i]}ms`,
                      width: `120px`,
                      height: `48px`,
                    }}
                    className={`sphere sphere-${i} flex items-center justify-center text-center`}
                    onClick={e => {
                      setOpen(false);
                      if (pathname === "/") {
                        e.preventDefault();
                        scrollToSection("contact-section");
                      } else {
                        e.preventDefault();
                        router.push('/#contact-section');
                      }
                    }}
                  >
                    {link.name}
                  </a>
                ) : (
                  <Link
                    key={link.name}
                    href={link.href}
                    style={{
                      animationDelay: `${sphereDelays[i]}ms`,
                      width: `120px`,
                      height: `48px`,
                    }}
                    className={`sphere sphere-${i} flex items-center justify-center text-center`}
                    onClick={() => setOpen(false)}
                  >
                    {link.name}
                  </Link>
                )
              ))}
            </div>
          )}
        </div>
      </div>
    </header>
  )
}

export default Header;
