import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Calendar, Users, Award, TrendingUp } from "lucide-react"

export default function OurStory() {
  return (
    <section className="py-0 min-h-screen">
      <div className="w-full h-full">
        <div className="grid lg:grid-cols-2 gap-0 min-h-screen">
          
          {/* Left Content */}
          <div
            className="p-6 sm:p-8 md:p-12 lg:p-16 xl:p-20 flex flex-col justify-center h-full"
            style={{ background: 'linear-gradient(180deg, #f9fafb 0%, #ffffff 50%, #f3f4f6 100%)' }}
          >
            <div className="max-w-2xl mx-auto lg:mx-0">
              <span className="text-xs uppercase tracking-widest text-[#007cef] mb-4 block font-semibold">
                Our Journey
              </span>
              <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 mb-6 leading-tight">
                From Vision to
                <br />
                <span className="text-[#007cef]">Global</span> Impact
              </h2>
              <p className="text-gray-600 mb-6 text-base sm:text-lg leading-relaxed">
                Founded with a simple yet ambitious vision: to bridge the gap between 
                groundbreaking ideas and their real-world application.
              </p>
              <p className="text-gray-600 mb-8 text-sm sm:text-base leading-relaxed">
                We've grown into a world-leading product innovation team, collaborating with 
                ambitious companies using our unique "Think Beyond™" process that guides us 
                from initial concept to market launch.
              </p>

              {/* Stats */}
              <div className="grid grid-cols-2 gap-4 sm:gap-6 mb-8">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 sm:w-10 sm:h-10 bg-[#007cef]/10 rounded-lg flex items-center justify-center flex-shrink-0">
                    <Calendar className="w-4 h-4 sm:w-5 sm:h-5 text-[#007cef]" />
                  </div>
                  <div>
                    <div className="text-xl sm:text-2xl font-bold text-gray-900">2025</div>
                    <div className="text-xs sm:text-sm text-gray-500">Founded</div>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 sm:w-10 sm:h-10 bg-green-500/10 rounded-lg flex items-center justify-center flex-shrink-0">
                    <Users className="w-4 h-4 sm:w-5 sm:h-5 text-green-600" />
                  </div>
                  <div>
                    <div className="text-xl sm:text-2xl font-bold text-gray-900">50+</div>
                    <div className="text-xs sm:text-sm text-gray-500">Team Members</div>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 sm:w-10 sm:h-10 bg-purple-500/10 rounded-lg flex items-center justify-center flex-shrink-0">
                    <Award className="w-4 h-4 sm:w-5 sm:h-5 text-purple-600" />
                  </div>
                  <div>
                    <div className="text-xl sm:text-2xl font-bold text-gray-900">100+</div>
                    <div className="text-xs sm:text-sm text-gray-500">Projects</div>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 sm:w-10 sm:h-10 bg-orange-500/10 rounded-lg flex items-center justify-center flex-shrink-0">
                    <TrendingUp className="w-4 h-4 sm:w-5 sm:h-5 text-orange-600" />
                  </div>
                  <div>
                    <div className="text-xl sm:text-2xl font-bold text-gray-900">200%</div>
                    <div className="text-xs sm:text-sm text-gray-500">Growth Rate</div>
                  </div>
                </div>
              </div>

              <div className="flex flex-col sm:flex-row gap-4">
                <Button
                  className="bg-[#007cef] hover:bg-blue-600 text-white px-6 py-3 rounded-lg font-medium transition-all duration-300 w-full sm:w-auto"
                >
                  Learn More
                </Button>
                <Button
                  variant="outline"
                  className="border-2 border-gray-900 text-gray-900 hover:bg-gray-900 hover:text-white px-6 py-3 bg-transparent w-full sm:w-auto"
                >
                  Our Process
                </Button>
              </div>
            </div>
          </div>

          {/* Right Visual Section */}
          <div className="bg-gray-100 p-6 sm:p-8 md:p-12 lg:p-16 xl:p-20 flex items-center justify-center relative">
            <div className="relative z-10 w-full max-w-lg mx-auto">
              {/* Main image container */}
              <div className="bg-white/80 backdrop-blur-sm border border-gray-200 rounded-2xl p-6 sm:p-8 shadow-xl">
                <div className="flex items-center justify-center h-64 sm:h-80 md:h-96">
                  <Image
                    src="/up (5).png"
                    alt="Our Story"
                    width={400}
                    height={400}
                    className="w-full h-full object-contain"
                  />
                </div>
              </div>
              
              {/* Floating timeline elements */}
              <div className="absolute -top-4 sm:-top-6 -left-4 sm:-left-6 bg-[#007cef] text-white p-3 sm:p-4 rounded-xl shadow-lg">
                <div className="text-lg sm:text-xl font-bold">2025</div>
                <div className="text-xs">Started</div>
              </div>
              
              <div className="absolute top-1/3 -right-6 sm:-right-8 bg-white text-gray-900 p-2 sm:p-3 rounded-lg shadow-lg border">
                <div className="text-sm sm:text-lg font-bold">Think Beyond™</div>
                <div className="text-xs">Our Process</div>
              </div>
              
              <div className="absolute -bottom-4 sm:-bottom-6 -right-4 sm:-right-6 bg-green-500 text-white p-3 sm:p-4 rounded-xl shadow-lg">
                <div className="text-lg sm:text-xl font-bold">Global</div>
                <div className="text-xs">Impact</div>
              </div>
            </div>

            {/* Background decorative elements */}
            <div className="absolute inset-0 hidden md:block">
              <div className="absolute top-8 right-8 w-12 h-12 lg:w-16 lg:h-16 border-2 border-[#007cef]/30 rounded-lg rotate-45"></div>
              <div className="absolute bottom-8 left-8 w-8 h-8 lg:w-12 lg:h-12 bg-gray-300/50 rounded-full"></div>
              <div className="absolute top-1/2 left-8 w-6 h-6 lg:w-8 lg:h-8 bg-[#007cef]/30 rounded-full"></div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
