import { Lightbulb, Target, Handshake } from "lucide-react"
import Image from "next/image"

export default function MissionVisionValues() {
  return (
    <section className="py-0 min-h-screen">
      <div className="w-full h-full">
        <div className="grid lg:grid-cols-2 gap-0 min-h-screen">
          
          {/* Left Content */}
          <div className="bg-black p-12 flex flex-col justify-center">
            <span className="text-xs uppercase tracking-widest text-[#007cef] mb-4 block font-semibold">
              Our Foundation
            </span>
            <h2 className="text-4xl lg:text-5xl font-bold text-white mb-6 leading-tight">
              Built on Strong
              <br />
              <span className="text-[#007cef]">Principles</span> &
              <br />
              Clear Vision
            </h2>
            <p className="text-gray-300 text-lg leading-relaxed mb-8">
              Our mission, vision, and values drive everything we do. They guide our decisions, 
              shape our culture, and define how we serve our clients and community.
            </p>

            {/* Mission, Vision, Values Cards */}
            <div className="space-y-6">
              {/* Mission */}
              <div className="bg-gray-900/50 backdrop-blur-sm border border-gray-700 rounded-xl p-6">
                <div className="flex items-start gap-4">
                  <div className="w-12 h-12 bg-[#007cef]/20 rounded-lg flex items-center justify-center flex-shrink-0">
                    <Lightbulb className="w-6 h-6 text-[#007cef]" />
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-white mb-2">Our Mission</h3>
                    <p className="text-gray-300 text-sm leading-relaxed">
                      To build scalable, modern software solutions that solve real-world problems—empowering 
                      startups and enterprises through technology, agility, and innovation.
                    </p>
                  </div>
                </div>
              </div>

              {/* Vision */}
              <div className="bg-gray-900/50 backdrop-blur-sm border border-gray-700 rounded-xl p-6">
                <div className="flex items-start gap-4">
                  <div className="w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center flex-shrink-0">
                    <Target className="w-6 h-6 text-green-400" />
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-white mb-2">Our Vision</h3>
                    <p className="text-gray-300 text-sm leading-relaxed">
                      To become a trusted global partner for digital transformation—recognized for quality, 
                      innovation, and the value we bring to every client project.
                    </p>
                  </div>
                </div>
              </div>

              {/* Values */}
              <div className="bg-gray-900/50 backdrop-blur-sm border border-gray-700 rounded-xl p-6">
                <div className="flex items-start gap-4">
                  <div className="w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center flex-shrink-0">
                    <Handshake className="w-6 h-6 text-purple-400" />
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-white mb-2">Our Values</h3>
                    <ul className="text-gray-300 text-sm space-y-1">
                      <li>• Innovation that matters</li>
                      <li>• Client-first mindset</li>
                      <li>• Transparent communication</li>
                      <li>• Ownership & accountability</li>
                      <li>• Growth through collaboration</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Right Visual Section */}
          <div className="bg-black flex items-center justify-center p-12 relative overflow-hidden">
            {/* Background decorative elements */}
            <div className="absolute inset-0">
              <div className="absolute top-20 right-20 w-32 h-32 bg-[#007cef]/10 rounded-full blur-xl"></div>
              <div className="absolute bottom-32 left-32 w-48 h-48 bg-purple-500/10 rounded-full blur-2xl"></div>
              <div className="absolute top-1/2 right-1/3 w-24 h-24 bg-green-400/10 rounded-full blur-lg"></div>
            </div>

            <div className="relative z-10 w-full max-w-md">
              {/* Main visual container */}
              <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-8 shadow-2xl">
                <div className="flex items-center justify-center h-80">
                  <Image
                    src="/up (4).png"
                    alt="Our Values"
                    width={300}
                    height={300}
                    className="w-full h-full object-contain opacity-90"
                  />
                </div>
              </div>
              
              {/* Floating metrics */}
              <div className="absolute -top-6 -right-6 bg-[#007cef] text-white p-4 rounded-xl shadow-lg">
                <div className="text-2xl font-bold">100%</div>
                <div className="text-xs">Commitment</div>
              </div>
              
              <div className="absolute -bottom-6 -left-6 bg-white text-black p-4 rounded-xl shadow-lg">
                <div className="text-2xl font-bold">5★</div>
                <div className="text-xs">Excellence</div>
              </div>

              <div className="absolute top-1/2 -left-8 bg-green-500 text-white p-3 rounded-lg shadow-lg">
                <div className="text-lg font-bold">∞</div>
                <div className="text-xs">Innovation</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
