"use client"

import { useState, useRef, useEffect } from "react"
import AboutHero from "@/components/about/AboutHero"
import OurS<PERSON> from "@/components/about/OurStory"
import MissionVisionValues from "@/components/about/MissionVisionValues"
import TeamSection from "@/components/about/TeamSection"
import Header from "@/components/Header"
import Footer from "@/components/Footer"
import { useHeaderTransparency } from "@/hooks/useHeaderTransparency"

export default function AboutPage() {
  const heroRef = useRef<HTMLElement>(null)
  const ourStoryRef = useRef<HTMLElement>(null)
  const missionRef = useRef<HTMLElement>(null)
  const teamRef = useRef<HTMLElement>(null)

  // Use the shared header transparency hook
  const headerTransparent = useHeaderTransparency([
    { ref: heroRef, isLightBackground: false }, // dark gradient background
    { ref: ourStoryRef, isLightBackground: true }, // light gray background
    { ref: missionRef, isLightBackground: false }, // black background
    { ref: teamRef, isLightBackground: true }, // light gray background
  ]);

  return (
    <div className="min-h-screen bg-white">
      {/* Fixed Header */}
      <div className="fixed top-0 left-0 w-full z-30">
        <Header transparent={headerTransparent} />
      </div>
    
      <main className="min-h-screen flex flex-col">
        <section ref={heroRef}>
          <AboutHero />
        </section>
        <section ref={ourStoryRef} className="py-20 px-4 sm:px-6 md:px-8 lg:px-12 xl:px-16 bg-gray-100">
          <OurStory />
        </section>
        <section ref={missionRef} className="py-20 px-4 sm:px-6 md:px-8 lg:px-12 xl:px-16 bg-black">
          <MissionVisionValues />
        </section>
        <section ref={teamRef} className="py-20 px-4 sm:px-6 md:px-8 lg:px-12 xl:px-16 bg-gray-100">
          <TeamSection />
        </section>
      </main>

      {/* Footer */}
      <Footer />
    </div>
  )
}
