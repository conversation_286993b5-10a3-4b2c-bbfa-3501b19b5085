import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Linkedin, Twitter, Github, Mail } from "lucide-react"

const teamMembers = [
  {
    name: "<PERSON>",
    title: "CEO & Founder",
    image: "/placeholder-user.jpg",
    description: "Visionary leader driving innovation and growth",
    expertise: ["Strategy", "Leadership", "Innovation"],
    social: { linkedin: "#", twitter: "#", email: "<EMAIL>" }
  },
  {
    name: "<PERSON>", 
    title: "Chief Product Officer",
    image: "/placeholder-user.jpg",
    description: "Product architect bringing ideas to life",
    expertise: ["Product Design", "UX/UI", "Strategy"],
    social: { linkedin: "#", github: "#", email: "<EMAIL>" }
  },
  {
    name: "<PERSON>",
    title: "Head of Design",
    image: "/placeholder-user.jpg", 
    description: "Creative force behind beautiful experiences",
    expertise: ["UI/UX Design", "Branding", "Research"],
    social: { linkedin: "#", twitter: "#", email: "<EMAIL>" }
  },
  {
    name: "<PERSON>",
    title: "Lead Engineer",
    image: "/placeholder-user.jpg",
    description: "Technical excellence and innovation",
    expertise: ["Full-Stack", "DevOps", "Architecture"],
    social: { linkedin: "#", github: "#", email: "<EMAIL>" }
  },
]

export default function TeamSection() {
  return (
    <section className="py-0 min-h-screen">
      <div className="w-full h-full">
        <div className="grid lg:grid-cols-2 gap-0 min-h-screen">
          
          {/* Left Content */}
          <div className="bg-gray-100 p-6 sm:p-8 md:p-12 lg:p-16 xl:p-20 flex flex-col justify-center">
            <div className="max-w-2xl mx-auto lg:mx-0">
              <span className="text-xs uppercase tracking-widest text-[#007cef] mb-4 block font-semibold">
                Our People
              </span>
              <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 mb-6 leading-tight">
                Meet the
                <br />
                <span className="text-[#007cef]">Visionaries</span>
                <br />
                Behind Nyx
              </h2>
              <p className="text-base sm:text-lg text-gray-600 leading-relaxed mb-8">
                Our diverse team of experts brings together decades of experience 
                in technology, design, and innovation. We're passionate about creating 
                exceptional digital experiences that make a real difference.
              </p>

              {/* Team highlights */}
              <div className="space-y-3 sm:space-y-4 mb-8">
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-[#007cef] rounded-full flex-shrink-0"></div>
                  <span className="text-sm sm:text-base text-gray-700">10+ Years Average Experience</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-green-500 rounded-full flex-shrink-0"></div>
                  <span className="text-sm sm:text-base text-gray-700">Global Talent from 5+ Countries</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-purple-500 rounded-full flex-shrink-0"></div>
                  <span className="text-sm sm:text-base text-gray-700">Cross-functional Expertise</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-orange-500 rounded-full flex-shrink-0"></div>
                  <span className="text-sm sm:text-base text-gray-700">Continuous Learning Culture</span>
                </div>
              </div>

              <div className="flex flex-col sm:flex-row gap-4">
              </div>
            </div>
          </div>

          {/* Right Team Grid */}
          <div className="bg-gray-100 p-6 sm:p-8 md:p-12 lg:p-16 xl:p-20 flex items-center justify-center">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6 max-w-lg w-full">
              {teamMembers.map((member, index) => (
                <div key={index} className="bg-white rounded-xl p-4 sm:p-6 shadow-lg border border-gray-200 hover:shadow-xl transition-shadow duration-300">
                  <div className="flex flex-col items-center text-center">
                    <div className="relative mb-3 sm:mb-4">
                      <Image
                        src={member.image}
                        alt={member.name}
                        width={80}
                        height={80}
                        className="rounded-full object-cover w-16 h-16 sm:w-20 sm:h-20"
                      />
                      <div className="absolute -bottom-1 sm:-bottom-2 -right-1 sm:-right-2 w-5 h-5 sm:w-6 sm:h-6 bg-[#007cef] rounded-full flex items-center justify-center">
                        <div className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-white rounded-full"></div>
                      </div>
                    </div>
                    
                    <h3 className="text-base sm:text-lg font-semibold text-gray-900 mb-1">{member.name}</h3>
                    <p className="text-[#007cef] text-xs sm:text-sm font-medium mb-2">{member.title}</p>
                    <p className="text-gray-600 text-xs leading-relaxed mb-3">{member.description}</p>
                    
                    {/* Expertise tags */}
                    <div className="flex flex-wrap gap-1 mb-3 justify-center">
                      {member.expertise.slice(0, 2).map((skill, i) => (
                        <span key={i} className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full">
                          {skill}
                        </span>
                      ))}
                    </div>
                    
                    {/* Social links */}
                    <div className="flex gap-2 justify-center">
                      {member.social.linkedin && (
                        <a href={member.social.linkedin} className="w-6 h-6 bg-gray-100 rounded-full flex items-center justify-center hover:bg-[#007cef] hover:text-white transition-colors">
                          <Linkedin className="w-3 h-3" />
                        </a>
                      )}
                      {member.social.twitter && (
                        <a href={member.social.twitter} className="w-6 h-6 bg-gray-100 rounded-full flex items-center justify-center hover:bg-[#007cef] hover:text-white transition-colors">
                          <Twitter className="w-3 h-3" />
                        </a>
                      )}
                      {member.social.github && (
                        <a href={member.social.github} className="w-6 h-6 bg-gray-100 rounded-full flex items-center justify-center hover:bg-[#007cef] hover:text-white transition-colors">
                          <Github className="w-3 h-3" />
                        </a>
                      )}
                      {member.social.email && (
                        <a href={`mailto:${member.social.email}`} className="w-6 h-6 bg-gray-100 rounded-full flex items-center justify-center hover:bg-[#007cef] hover:text-white transition-colors">
                          <Mail className="w-3 h-3" />
                        </a>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
