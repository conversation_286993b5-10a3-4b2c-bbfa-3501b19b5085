"use client"

import Image from "next/image"
import Link from "next/link"
import Header from "@/components/Header"
import Footer from "@/components/Footer"

export default function BlogPage() {
  const blogPosts = [
    {
      id: 1,
      title: "The Future of Digital Product Innovation: Trends Shaping 2024",
      excerpt: "Explore the latest trends in AI-first design, sustainable UX, and hyper-personalization that are revolutionizing how we build digital products.",
      author: "<PERSON>",
      authorRole: "Lead Product Designer",
      date: "January 15, 2024",
      readTime: "8 min read",
      category: "Product Innovation",
      image: "/placeholder.jpg",
      slug: "sample-post"
    },
    {
      id: 2,
      title: "Building Scalable Design Systems for Modern Teams",
      excerpt: "Learn how to create design systems that grow with your team and maintain consistency across all your digital products.",
      author: "<PERSON>",
      authorRole: "Design System Lead",
      date: "January 10, 2024",
      readTime: "6 min read",
      category: "Design Systems",
      image: "/placeholder.jpg",
      slug: "#"
    },
    {
      id: 3,
      title: "The Psychology of User Onboarding: First Impressions Matter",
      excerpt: "Discover the psychological principles behind effective user onboarding and how to create memorable first experiences.",
      author: "<PERSON>",
      authorRole: "UX Researcher",
      date: "January 5, 2024",
      readTime: "5 min read",
      category: "User Experience",
      image: "/placeholder.jpg",
      slug: "#"
    }
  ]

  return (
    <div className="min-h-screen bg-white">
      {/* Fixed Header */}
      <div className="fixed top-0 left-0 w-full z-30">
        <Header transparent={false} />
      </div>

      {/* Spacer for Navbar height */}
      <div className="h-16 sm:h-20 lg:h-[80px]" />

      <main className="container mx-auto px-4 sm:px-6 lg:px-8 py-8 sm:py-12 md:py-16">
        {/* Hero Section */}
        <div className="text-center mb-12 sm:mb-16">
          <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold text-gray-900 mb-6">
            Our <span className="text-blue-600">Blog</span>
          </h1>
          <p className="text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Insights, trends, and stories from the world of digital product innovation.
            Stay ahead with expert perspectives on design, technology, and user experience.
          </p>
        </div>

        {/* Featured Post */}
        <div className="mb-16">
          <div className="relative">
            <Link href={`/blog/${blogPosts[0].slug}`}>
              <div
                className="group relative bg-white rounded-2xl overflow-hidden transition-all duration-300 hover:scale-[1.02]"
                style={{
                  transform: 'perspective(1000px) rotateX(1deg)',
                  boxShadow: '0 20px 40px rgba(0,0,0,0.1), 0 8px 16px rgba(0,0,0,0.06)'
                }}
              >
                <div className="grid lg:grid-cols-2 gap-0">
                  <div className="relative h-64 sm:h-80 lg:h-96">
                    <Image
                      src={blogPosts[0].image}
                      alt={blogPosts[0].title}
                      fill
                      className="object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                    <div className="absolute top-4 left-4">
                      <span className="bg-blue-600 text-white text-xs sm:text-sm font-medium px-3 py-1 rounded-full">
                        Featured
                      </span>
                    </div>
                  </div>
                  <div className="p-6 sm:p-8 lg:p-12 flex flex-col justify-center">
                    <div className="mb-4">
                      <span className="inline-block bg-blue-100 text-blue-800 text-xs sm:text-sm font-medium px-3 py-1 rounded-full">
                        {blogPosts[0].category}
                      </span>
                    </div>
                    <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 mb-4 group-hover:text-blue-600 transition-colors">
                      {blogPosts[0].title}
                    </h2>
                    <p className="text-gray-600 mb-6 leading-relaxed">
                      {blogPosts[0].excerpt}
                    </p>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <Image
                          src="/placeholder-user.jpg"
                          alt={blogPosts[0].author}
                          width={40}
                          height={40}
                          className="rounded-full mr-3"
                        />
                        <div>
                          <p className="font-medium text-gray-900">{blogPosts[0].author}</p>
                          <p className="text-sm text-gray-500">{blogPosts[0].authorRole}</p>
                        </div>
                      </div>
                      <div className="text-sm text-gray-500">
                        <time>{blogPosts[0].date}</time>
                        <span className="mx-2">•</span>
                        <span>{blogPosts[0].readTime}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </Link>
          </div>
        </div>

        {/* Blog Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {blogPosts.slice(1).map((post, index) => (
            <Link key={post.id} href={`/blog/${post.slug}`}>
              <article
                className="group bg-white rounded-xl overflow-hidden transition-all duration-300 hover:scale-105"
                style={{
                  transform: `perspective(800px) rotateY(${index % 2 === 0 ? '2deg' : '-2deg'})`,
                  boxShadow: '0 8px 20px rgba(0,0,0,0.08)',
                  animationDelay: `${index * 100}ms`
                }}
              >
                <div className="relative h-48 sm:h-56">
                  <Image
                    src={post.image}
                    alt={post.title}
                    fill
                    className="object-cover group-hover:scale-110 transition-transform duration-300"
                  />
                  <div className="absolute top-3 left-3">
                    <span className="bg-white/90 backdrop-blur-sm text-gray-800 text-xs font-medium px-2 py-1 rounded-full">
                      {post.category}
                    </span>
                  </div>
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors line-clamp-2">
                    {post.title}
                  </h3>
                  <p className="text-gray-600 mb-4 text-sm leading-relaxed line-clamp-3">
                    {post.excerpt}
                  </p>
                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center">
                      <Image
                        src="/placeholder-user.jpg"
                        alt={post.author}
                        width={32}
                        height={32}
                        className="rounded-full mr-2"
                      />
                      <span className="font-medium text-gray-900">{post.author}</span>
                    </div>
                    <div className="text-gray-500">
                      <span>{post.readTime}</span>
                    </div>
                  </div>
                </div>
              </article>
            </Link>
          ))}
        </div>

        {/* Newsletter Signup */}
        <div
          className="mt-16 bg-gradient-to-r from-gray-900 to-blue-900 text-white p-8 sm:p-12 rounded-2xl text-center"
          style={{
            transform: 'perspective(1000px) rotateX(-1deg)',
            boxShadow: '0 20px 40px rgba(0,0,0,0.2)'
          }}
        >
          <h2 className="text-2xl sm:text-3xl font-bold mb-4">Stay Updated</h2>
          <p className="text-gray-200 mb-6 max-w-2xl mx-auto">
            Get the latest insights on product innovation, design trends, and industry best practices delivered to your inbox.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
            <input
              type="email"
              placeholder="Enter your email"
              className="flex-1 px-4 py-3 rounded-lg text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <button className="bg-blue-600 hover:bg-blue-700 px-6 py-3 rounded-lg font-medium transition-colors">
              Subscribe
            </button>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  )
}