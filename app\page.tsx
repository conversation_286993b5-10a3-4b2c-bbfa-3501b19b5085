"use client"

import { But<PERSON> } from "@/components/ui/button"
import { Volume2, VolumeX } from "lucide-react"
import Image from "next/image"
import { useState, useRef, useEffect } from "react"
import ContactSection from "@/components/ContactSection"
import { useScrollToSection } from "@/components/useScrollToSection"
import Footer from "@/components/Footer"
import Header from "@/components/Header"

export default function HomePage() {
  const [muted, setMuted] = useState(true)
  const [userMuted, setUserMuted] = useState(true)
  const [navbarTransparent, setNavbarTransparent] = useState(false)
  const videoRef = useRef<HTMLVideoElement>(null)
  const heroRef = useRef<HTMLElement>(null)
  const firstSectionRef = useRef<HTMLElement>(null)
  const secondSectionRef = useRef<HTMLElement>(null)
  const thirdSectionRef = useRef<HTMLElement>(null)

  const scrollToSection = useScrollToSection();

  // Handle user mute/unmute
  const handleToggleMute = () => {
    setMuted((prev) => {
      const newMuted = !prev
      setUserMuted(!prev)
      if (videoRef.current) {
        videoRef.current.muted = newMuted
        if (!newMuted) {
          videoRef.current.volume = 1
        }
      }
      return newMuted
    })
  }

  // Navbar transparency logic
  useEffect(() => {
    const hero = heroRef.current
    const thirdSection = thirdSectionRef.current
    if (!hero || !thirdSection) return

    let isHeroVisible = false
    let isThirdVisible = false
    let hasPassedThird = false

    const handleNavState = () => {
      if (hasPassedThird || isThirdVisible) {
        setNavbarTransparent(false) // colored
      } else if (isHeroVisible) {
        setNavbarTransparent(false) // colored
      } else {
        setNavbarTransparent(true) // transparent
      }
    }

    const heroObserver = new window.IntersectionObserver(
      ([entry]) => {
        isHeroVisible = entry.isIntersecting
        handleNavState()
      },
      { threshold: 0.1 }
    )
    const thirdObserver = new window.IntersectionObserver(
      ([entry]) => {
        isThirdVisible = entry.isIntersecting
        handleNavState()
      },
      { threshold: 0.1 }
    )
    heroObserver.observe(hero)
    thirdObserver.observe(thirdSection)

    // Scroll event to detect if user has passed the third section
    const onScroll = () => {
      if (thirdSection) {
        const rect = thirdSection.getBoundingClientRect()
        hasPassedThird = rect.top <= 0
        handleNavState()
      }
    }
    window.addEventListener('scroll', onScroll)
    // Initial check
    onScroll()

    return () => {
      heroObserver.disconnect()
      thirdObserver.disconnect()
      window.removeEventListener('scroll', onScroll)
    }
  }, [])

  // Mute video when hero is out of view
  useEffect(() => {
    const hero = heroRef.current;
    if (!hero) return;

    const observer = new window.IntersectionObserver(
      ([entry]) => {
        if (!entry.isIntersecting) {
          setMuted(true);
          if (videoRef.current) videoRef.current.muted = true;
        }
      },
      { threshold: 0.1 }
    );
    observer.observe(hero);
    return () => observer.disconnect();
  }, []);

  // Scroll to contact section if hash is present
  useEffect(() => {
    if (typeof window !== 'undefined' && window.location.hash === '#contact-section') {
      setTimeout(() => {
        scrollToSection('contact-section');
      }, 100);
    }
  }, []);

  return (
    <div className="min-h-screen bg-white">
          {/* Fixed Header */}
          <div className="fixed top-0 left-0 w-full z-30">
            <Header transparent={navbarTransparent} />
          </div>
        
          {/* Spacer for Navbar height */}
          <div className="h-16 sm:h-20 lg:h-[80px]" /> {/* Responsive spacer to match header height */}
        
          {/* Hero Video Section */}
          <section
            ref={heroRef}
            className="relative w-full h-[calc(100vh-4rem)] sm:h-[calc(100vh-5rem)] lg:h-[calc(100vh-80px)] overflow-hidden p-0 m-0"
          >
            <video
              ref={videoRef}
              className="absolute top-0 left-0 w-full h-full object-cover z-0"
              autoPlay
              muted={muted}
              loop
              playsInline
              poster="/placeholder.svg?height=600&width=1200"
              src="/Heo page video.mp4"
            />
            {/* Khaki overlay layer
            <div
              className="absolute inset-0 z-10 pointer-events-none"
              style={{ background: "rgba(240, 230, 140, 0.1)" }}
            /> */}
            <div className="absolute inset-0 flex items-center justify-center z-20 pointer-events-none">
              {/* Optional overlay content */}
            </div>
            <button
              onClick={handleToggleMute}
              className="absolute bottom-4 right-4 sm:bottom-6 sm:right-6 z-30 bg-black/40 hover:bg-black/60 text-white rounded-full p-2 sm:p-3 transition-colors"
              style={{ backdropFilter: "blur(4px)" }}
              aria-label={muted ? "Unmute video" : "Mute video"}
            >
              {muted ? <VolumeX className="w-5 h-5 sm:w-7 sm:h-7" /> : <Volume2 className="w-5 h-5 sm:w-7 sm:h-7" />}
            </button>

            {/* Bottom-left hero wording and Contact Us button */}
            <div className="absolute bottom-4 left-4 sm:bottom-8 sm:left-8 md:bottom-12 md:left-12 lg:bottom-20 lg:left-20 lg:ml-6 z-10 flex flex-col items-start max-w-xs sm:max-w-md md:max-w-lg lg:max-w-2xl w-full">
              {/* Black transparent background */}
              <div className="absolute inset-0 bg-black/60 rounded-xl z-0" />
              <div className="relative z-10 p-3 sm:p-4 md:p-5 lg:p-6">
                <div className="mb-3 sm:mb-4 text-left">
                  <h2 className="text-lg sm:text-xl md:text-2xl lg:text-3xl font-semibold text-white drop-shadow leading-tight">
                    Turning imagination into reality.<br />
                    Making lasting impact.<br />
                    {/* For the world’s future-defining brands. */}
                    For the next generation of global brands.
                  </h2>
                </div>
                <button
                  className="w-full sm:w-48 md:w-52 lg:w-56 px-4 sm:px-5 md:px-6 py-2 sm:py-2.5 md:py-3 border border-white text-white bg-transparent rounded-lg font-medium hover:bg-white/10 transition backdrop-blur-sm text-sm sm:text-base"
                  style={{ boxShadow: '0 2px 8px 0 rgba(0,0,0,0.10)' }}
                  onClick={() => scrollToSection("contact-section")}
                >
                  Contact Us
                </button>
              </div>
            </div>
          </section>

  


      {/* First Content Section */}
      <section className="py-0 min-h-screen" ref={firstSectionRef}>
            <div className="w-full h-full">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-0 min-h-screen">

                {/* Left Content */}
                <div
                  className="p-6 sm:p-8 md:p-10 lg:p-12 xl:pl-20 flex flex-col justify-center h-full min-h-[50vh] lg:min-h-screen"
                  style={{ background: 'linear-gradient(180deg, #050a0b 0%, #FAFAFA 50%, #050a0b 100%)' }}
                >
                  <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-4 sm:mb-6 leading-tight">
                    We're a world
                    <br />
                    leading product
                    <br />
                    innovation team
                  </h2>
                  <p className="text-gray-600 mb-6 sm:mb-8 text-base sm:text-lg leading-relaxed">
                    Collaborating with ambitious companies to create
                    <br className="hidden sm:block" />
                    digital experiences used by billions everyday.
                  </p>
                  <div>
                    <Button
                      variant="outline"
                      className="border-2 border-gray-900 text-gray-900 hover:bg-gray-900 hover:text-white px-4 sm:px-6 py-2 sm:py-3 bg-transparent text-sm sm:text-base"
                    >
                      Our company
                    </Button>
                  </div>
                </div>

                {/* Right Blue Section */}
                <div className="bg-black flex items-center justify-center p-6 sm:p-8 md:p-10 lg:p-12 h-full min-h-[50vh] lg:min-h-screen">
                  <Image
                    src="/up (3).png"
                    alt="Logo"
                    width={500}
                    height={500}
                    className="w-32 h-32 sm:w-48 sm:h-48 md:w-64 md:h-64 lg:w-80 lg:h-80 xl:w-100 xl:h-100 object-contain"
                  />
                </div>

              </div>
            </div>
          </section>


     {/* Second Content Section */}
        <section className="pb-16 sm:pb-24 md:pb-32" ref={secondSectionRef}>
          <div className="w-full">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-0 min-h-[400px] sm:min-h-[500px]">

              {/* Left Content */}
              <div className="bg-black p-6 sm:p-8 md:p-10 lg:p-12 flex flex-col justify-center min-h-[50vh] lg:min-h-auto">
                <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-gray-500 mb-4 sm:mb-6 leading-tight">
                  We work from
                  <br />
                  strategy to
                  <br />
                  execution
                </h2>
                <p className="text-gray-100 text-base sm:text-lg leading-relaxed">
                  Using our Think Beyond™ process to help partners
                  <br className="hidden sm:block" />
                  identify clear opportunity and launch market-leading
                  <br className="hidden sm:block" />
                  products and services.
                </p>
              </div>

              {/* Right Image Section */}
              <div className="relative overflow-hidden max-h-[400px] sm:max-h-[600px] md:max-h-[800px] min-h-[50vh] lg:min-h-auto">
                <video
                  className="w-full h-full object-cover"
                  autoPlay
                  loop
                  muted
                  playsInline
                  src="/3256768-uhd_1440_2560_25fps.mp4"
                />

                {/* Gradient overlay */}
                <div className="absolute inset-0 pointer-events-none bg-gradient-to-b from-black via-transparent to-black opacity-60" />
              </div>



            </div>
          </div>
        </section>


      {/* Third Content Section (Welcome Wheels Up!) */}
      <section className="py-0 mt-0 mb-10 sm:mb-16 md:mb-20 pb-6 sm:pb-8 md:pb-10" ref={thirdSectionRef}>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8" style={{ boxShadow: '0 12px 32px 0 rgba(30,41,59,0.25), 0 2px 8px 0 rgba(0,0,0,0.10)', borderRadius: '2rem', transform: 'perspective(900px) rotateX(4deg) scale(1.01)', background: '#fff' }}>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-0 min-h-[300px] sm:min-h-[400px]">
            {/* Left Content */}
            <div className="bg-gray-100 p-6 sm:p-8 md:p-10 lg:p-12 flex flex-col justify-center rounded-t-2xl lg:rounded-t-none lg:rounded-l-3xl">
              <span className="text-xs uppercase tracking-widest text-gray-400 mb-2">April 2024</span>
              <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 mb-4 sm:mb-6 leading-tight">
                Welcome
                <br />
                Wheels Up!
              </h2>
              <p className="text-gray-600 text-sm sm:text-base leading-relaxed">
                Our major step to a new level for product innovation.
              </p>
            </div>
            {/* Right Blue Section */}
            <div className="bg-gray-100 flex items-center justify-center relative rounded-b-2xl lg:rounded-b-none lg:rounded-r-3xl overflow-hidden min-h-[200px] sm:min-h-[300px] lg:min-h-[400px]">
              <div
                className="relative w-full h-full flex items-center justify-center p-4 sm:p-6 lg:p-8"
                style={{
                  transform: 'perspective(900px) rotateY(-8deg) rotateX(2deg)',
                  transformStyle: 'preserve-3d',
                  transition: 'transform 0.3s ease-out'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.transform = 'perspective(900px) rotateY(-12deg) rotateX(4deg) scale(1.05)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.transform = 'perspective(900px) rotateY(-8deg) rotateX(2deg)';
                }}
              >
                <Image
                  src="/prj1.png"
                  alt="Logo"
                  fill
                  style={{
                    objectFit: 'contain',
                    filter: 'drop-shadow(0 8px 16px rgba(0,0,0,0.15))',
                  }}
                  className="w-full h-full object-contain"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Gallery/Showcase Section */}
      <section className="py-8 sm:py-12 md:py-16" style={{ background: 'linear-gradient(90deg, #000 10%, #111827 50%, #000 100%)' }}>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-xl sm:text-2xl font-bold mb-8 sm:mb-12 md:mb-20 text-center text-white">Gallery</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6">
            {[
              { url: "https://nyxsaas.com", title: "Projects", caption: "Oof the hard work paid off..." },
              { url: " https://anupatehan.github.io/coffee-shop/# ", title: "Projects", caption: "Another amazing launch!" },
              { url: "https://nyxsaas.com", title: "Projects", caption: "Oof the hard work paid off..." },
              { url: "https://nyxsaas.com", title: "Projects", caption: "Another amazing launch!" },
              // { url: "https://nyxsaas.com", title: "Project 1", caption: "Oof the hard work paid off..." },
              // { url: "https://nyxsaas.com", title: "Project 2", caption: "Another amazing launch!" },
              // { url: "https://nyxsaas.com", title: "Project 1", caption: "Oof the hard work paid off..." },
              // { url: "https://nyxsaas.com", title: "Project 2", caption: "Another amazing launch!" },
              // Add more projects here
            ].map((project, i) => {
              const [dark, setDark] = useState(false);
              return (
              <div
                key={i}
                  className={`${dark ? 'bg-gray-900 text-white border-gray-700' : 'bg-gray-200 text-gray-900 border-gray-200'} rounded-2xl shadow-lg border flex flex-col overflow-hidden w-full max-w-sm mx-auto`}
                onDoubleClick={() => window.open(project.url, '_blank')}
                style={{ cursor: 'pointer', userSelect: 'none' }}
              >
                {/* Top bar */}
                <div className="flex items-center px-3 sm:px-4 py-2 sm:py-3 border-b border-gray-100">
                  <img
                    src="/placeholder-logo.png"
                    alt={project.title}
                    className="w-6 h-6 sm:w-8 sm:h-8 rounded-full mr-2 sm:mr-3"
                  />
                    <span className={`font-semibold text-xs sm:text-sm flex-1 ${dark ? 'text-white' : 'text-gray-800'}`}>{project.title}</span>
                    <button
                      className={`ml-1 sm:ml-2 p-1 rounded-full border border-gray-300 bg-white hover:bg-gray-100 focus:outline-none focus:ring ${dark ? 'bg-gray-800 text-yellow-300 border-gray-600' : 'bg-white text-gray-700'}`}
                      style={{ minWidth: 28, minHeight: 28, display: 'flex', alignItems: 'center', justifyContent: 'center' }}
                      onClick={e => { e.stopPropagation(); setDark(d => !d); }}
                      title="Toggle card theme"
                    >
                      {dark ? (
                        // Sun icon for dark mode
                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="none" viewBox="0 0 24 24"><path fill="currentColor" d="M12 4.75a.75.75 0 0 1 .75.75v1a.75.75 0 0 1-1.5 0v-1A.75.75 0 0 1 12 4.75Zm0 12.5a.75.75 0 0 1 .75.75v1a.75.75 0 0 1-1.5 0v-1a.75.75 0 0 1 .75-.75Zm7.25-5.25a.75.75 0 0 1 .75.75v.5a.75.75 0 0 1-1.5 0v-.5a.75.75 0 0 1 .75-.75Zm-12.5 0a.75.75 0 0 1 .75.75v.5a.75.75 0 0 1-1.5 0v-.5a.75.75 0 0 1 .75-.75Zm9.19-4.44a.75.75 0 0 1 1.06 0l.35.35a.75.75 0 1 1-1.06 1.06l-.35-.35a.75.75 0 0 1 0-1.06Zm-8.48 8.48a.75.75 0 0 1 1.06 0l.35.35a.75.75 0 1 1-1.06 1.06l-.35-.35a.75.75 0 0 1 0-1.06Zm8.48 8.48a.75.75 0 0 1 0-1.06l.35-.35a.75.75 0 1 1 1.06 1.06l-.35.35a.75.75 0 0 1-1.06 0Zm-8.48-8.48a.75.75 0 0 1 0-1.06l.35-.35a.75.75 0 1 1 1.06 1.06l-.35.35a.75.75 0 0 1-1.06 0ZM12 7.5A4.5 4.5 0 1 1 7.5 12 4.5 4.5 0 0 1 12 7.5Z"/></svg>
                      ) : (
                        // Moon icon for light mode
                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="none" viewBox="0 0 24 24"><path fill="currentColor" d="M21.75 12.002a9.75 9.75 0 0 1-9.748 9.748c-2.62 0-5.01-1.02-6.84-2.85a.75.75 0 0 1 .53-1.28c.09 0 .18.02.26.06A8.25 8.25 0 1 0 12 3.752a.75.75 0 0 1-.22-1.47c.08-.03.17-.05.26-.05a9.75 9.75 0 0 1 9.75 9.75Z"/></svg>
                      )}
                    </button>
                </div>
                {/* Main content */}
                <div className="relative w-full aspect-square flex items-center justify-center overflow-auto hide-scrollbar bg-gray-100">
                  <div className="w-full" style={{ height: '100%', minHeight: 0, minWidth: 0 }}>
                    <iframe
                      src={project.url}
                      title={project.title}
                      className="w-full"
                      style={{ height: '400px', minHeight: 0, minWidth: 0, background: '#fafafa', overflow: 'hidden' }}
                      sandbox="allow-scripts allow-same-origin"
                      scrolling="no"
                    />
                  </div>
                </div>
                {/* Caption */}
                  <div className={`px-3 sm:px-4 py-2 sm:py-3 border-t border-gray-100 text-xs sm:text-sm ${dark ? 'text-white' : 'text-gray-700'}`}>
                  {project.caption || "Check out our latest project!"}
                </div>
              </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Blog Preview Section */}
      <section className="py-8 sm:py-12 md:py-16 bg-gray-50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-xl sm:text-2xl font-bold mb-6 sm:mb-8 text-center">From our blog</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8">
            {[1,2,3].map(i => (
              <div key={i} className="bg-white rounded-lg shadow p-4 sm:p-6 flex flex-col items-center">
                <Image
                  src={`/placeholder-logo.png`}
                  alt={`Blog post ${i}`}
                  width={200}
                  height={120}
                  className="object-cover rounded mb-3 sm:mb-4 w-full max-w-[200px]"
                />
                <h3 className="font-semibold text-base sm:text-lg mb-2 text-center">Blog Post Title {i}</h3>
                <p className="text-gray-600 text-xs sm:text-sm mb-3 sm:mb-4 text-center">Short description for blog post {i}. This is placeholder content.</p>
                <Button variant="outline" className="border-gray-900 text-gray-900 hover:bg-gray-900 hover:text-white px-3 sm:px-4 py-1.5 sm:py-2 text-sm">Read More</Button>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-8 sm:py-12 md:py-16">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-white rounded-lg shadow p-6 sm:p-8 flex flex-col md:flex-row justify-between items-center gap-6">
            <div className="text-center md:text-left">
              <h2 className="text-xl sm:text-2xl font-bold mb-2">Contact Us</h2>
              <p className="text-gray-600 text-sm sm:text-base"><EMAIL></p>
              <p className="text-gray-600 text-sm sm:text-base">1234 Innovation Drive, City, Country</p>
            </div>
            <div>
              <Button variant="default" className="px-4 sm:px-6 py-2 sm:py-3 text-sm sm:text-base">Send a Message</Button>
            </div>
          </div>
        </div>
      </section>

      <section id="contact-section">
        <div>
          <ContactSection />
        </div>
      </section>

      {/* Footer */}
      <Footer />
    </div>
  )
}
