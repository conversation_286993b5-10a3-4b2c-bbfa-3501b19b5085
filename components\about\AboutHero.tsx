'use client';

import React from 'react';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { useScrollToSection } from '@/components/useScrollToSection';

const AboutHero = () => {
  const scrollToSection = useScrollToSection();

  return (
    <section className="relative w-full h-screen overflow-hidden">
      {/* Background with gradient overlay */}
      <div 
        className="absolute inset-0 z-0"
        style={{ 
          background: 'linear-gradient(135deg, #000000 0%, #1a1a1a 50%, #007cef 100%)',
        }}
      />
      
      {/* Animated background elements */}
      <div className="absolute inset-0 z-10">
        <div className="absolute top-20 left-20 w-32 h-32 bg-[#007cef]/20 rounded-full blur-xl animate-pulse"></div>
        <div className="absolute bottom-32 right-32 w-48 h-48 bg-white/10 rounded-full blur-2xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/3 w-24 h-24 bg-[#007cef]/30 rounded-full blur-lg animate-pulse delay-500"></div>
      </div>

      {/* Main content */}
      <div className="relative z-20 h-full flex items-center">
        <div className="container mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            
            {/* Left content */}
            <div className="text-white space-y-8">
              <div>
                <span className="text-xs uppercase tracking-widest text-[#007cef] mb-4 block font-semibold">
                  About Our Company
                </span>
                <h1 className="text-4xl lg:text-6xl font-bold leading-tight mb-6">
                  Innovating the
                  <br />
                  <span className="text-[#007cef]">Future</span> of
                  <br />
                  Technology
                </h1>
                <p className="text-xl text-gray-300 leading-relaxed mb-8">
                  We're a passionate team of creators, innovators, and dreamers 
                  building the next generation of digital experiences that shape 
                  how the world connects and grows.
                </p>
              </div>

              <div className="flex flex-col sm:flex-row gap-4">
                <Button
                  className="border-2 border-[#007cef] bg-[#007cef] text-white hover:bg-white hover:text-[#007cef] hover:border-[#007cef] px-8 py-3 rounded-lg font-medium transition-all duration-300"
                  onClick={() => scrollToSection("contact-section")}
                >
                  Get in Touch
                </Button>
                <Button
                  variant="outline"
                  className="border-2 border-white text-black hover:bg-[#007cef] hover:text-white hover:border-[#007cef] px-8 py-3 rounded-lg font-medium transition-all duration-300"
                >
                  Our Story
                </Button>
              </div>

              {/* Stats */}
              <div className="grid grid-cols-3 gap-8 pt-8 border-t border-gray-700">
                <div className="text-center lg:text-left">
                  <div className="text-3xl font-bold text-[#007cef] mb-2">2+</div>
                  <div className="text-sm text-gray-400 uppercase tracking-wide">Years Experience</div>
                </div>
                <div className="text-center lg:text-left">
                  <div className="text-3xl font-bold text-[#007cef] mb-2">10+</div>
                  <div className="text-sm text-gray-400 uppercase tracking-wide">Projects Completed</div>
                </div>
                <div className="text-center lg:text-left">
                  <div className="text-3xl font-bold text-[#007cef] mb-2">0+</div>
                  <div className="text-sm text-gray-400 uppercase tracking-wide">Happy Clients</div>
                </div>
              </div>
            </div>

            {/* Right content - Image/Visual */}
            <div className="relative">
              <div className="relative z-10">
                {/* Main image container */}
                <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl p-8 shadow-2xl">
                  <div className="flex items-center justify-center h-80">
                    <Image
                      src="/up (3).png"
                      alt="About Us"
                      width={300}
                      height={300}
                      className="w-full h-full object-contain"
                    />
                  </div>
                </div>
                
                {/* Floating elements */}
                <div className="absolute -top-6 -right-6 bg-[#007cef] text-white p-4 rounded-xl shadow-lg">
                  <div className="text-2xl font-bold">2025</div>
                  <div className="text-xs">Founded</div>
                </div>
                
                <div className="absolute -bottom-6 -left-6 bg-white text-black p-4 rounded-xl shadow-lg">
                  <div className="text-2xl font-bold">∞</div>
                  <div className="text-xs">Possibilities</div>
                </div>
              </div>

              {/* Background decorative elements */}
              <div className="absolute inset-0 -z-10">
                <div className="absolute top-8 right-8 w-16 h-16 border-2 border-[#007cef]/50 rounded-lg rotate-45"></div>
                <div className="absolute bottom-8 left-8 w-12 h-12 bg-white/20 rounded-full"></div>
                <div className="absolute top-1/2 right-0 w-8 h-8 bg-[#007cef]/60 rounded-full"></div>
              </div>
            </div>

          </div>
        </div>
      </div>

      {/* Scroll indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20">
        <div className="flex flex-col items-center text-white/60">
          <div className="text-xs uppercase tracking-widest mb-2">Scroll</div>
          <div className="w-px h-12 bg-white/40 animate-pulse"></div>
        </div>
      </div>
    </section>
  );
};

export default AboutHero;
